import '../../prepare_for_app_initiation.dart';
import '../../resources/ui_strings.dart';
import '../../widget/evo_mpin_code/evo_mpin_code_config.dart';

final MpinValidator mpinValidator = getIt.get<MpinValidator>();

/// Validator for MPIN validation with advanced security checks
class MpinValidator {
  static MpinValidator? _instance;

  MpinValidator._();

  factory MpinValidator() {
    return _instance ??= MpinValidator._();
  }

  /// Extracts digits from a PIN string into a list of integers
  List<int>? extractToListDigit(String mpin) {
    final List<int> digits = <int>[];

    for (final String char in mpin.split('')) {
      final int? digit = int.tryParse(char);
      if (digit == null) {
        return null;
      }
      digits.add(digit);
    }

    return digits;
  }

  /// Checks if the PIN contains a decreasing sequence (e.g., 4321)
  bool isDecreasingSequence(List<int> digits) {
    if (digits.length <= 1) {
      return false;
    }

    bool isDecreasing = true;
    for (int i = 0; i < digits.length - 1; i++) {
      if (digits[i] != digits[i + 1] + 1) {
        isDecreasing = false;
        break;
      }
    }
    return isDecreasing;
  }

  /// Checks if the PIN contains an increasing sequence (e.g., 1234)
  bool isIncreasingSequence(List<int> digits) {
    if (digits.length <= 1) {
      return false;
    }

    bool isIncreasing = true;
    for (int i = 0; i < digits.length - 1; i++) {
      if (digits[i] != digits[i + 1] - 1) {
        isIncreasing = false;
        break;
      }
    }
    return isIncreasing;
  }

  /// Checks if all digits in the PIN are the same (e.g., 1111)
  bool isAllSameDigits(List<int> digits) {
    if (digits.isEmpty) {
      return false;
    }

    final int firstDigit = digits.first;
    return digits.every((digit) => digit == firstDigit);
  }

  /// Checks if the PIN follows all security requirements
  String? validatePin(String pin) {
    if (pin.isEmpty) {
      return UIStrings.enterMpin;
    }

    if (pin.length != EvoMPINCodeConfig.defaultMPINCodeLength) {
      return UIStrings.pleaseEnterXDigitMpin(EvoMPINCodeConfig.defaultMPINCodeLength);
    }

    final List<int>? digits = extractToListDigit(pin);
    if (digits == null) {
      return UIStrings.pinCanOnlyContainNumbers;
    }

    if (isAllSameDigits(digits)) {
      return UIStrings.pinCannotContainSameDigits;
    }

    if (isIncreasingSequence(digits)) {
      return UIStrings.pinCannotBeSequential;
    }

    if (isDecreasingSequence(digits)) {
      return UIStrings.pinCannotBeSequential;
    }

    return null; // No validation errors
  }

  /// Validates that two PINs match for confirmation
  String? validatePinConfirmation(String pin, String confirmPin) {
    if (confirmPin.isEmpty) {
      return UIStrings.enterConfirmMpin;
    }

    if (pin != confirmPin) {
      return UIStrings.mpinDoesntMatch;
    }

    return null; // No validation errors
  }
}
