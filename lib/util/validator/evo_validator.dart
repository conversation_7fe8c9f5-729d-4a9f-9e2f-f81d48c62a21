import '../../prepare_for_app_initiation.dart';
import '../../widget/evo_mpin_code/evo_mpin_code_config.dart';

EvoValidator get evoValidator => getIt.get<EvoValidator>();

/// Utility class for common validations throughout the app
class EvoValidator {
  /// Validates whether a PIN has the correct length
  bool validateMaxLengthPin(String value) {
    return value.length == EvoMPINCodeConfig.defaultMPINCodeLength;
  }
}
