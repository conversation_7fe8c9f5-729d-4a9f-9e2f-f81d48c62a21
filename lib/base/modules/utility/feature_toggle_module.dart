import 'package:get_it/get_it.dart';
import 'package:flutter_common_package/base/module/feature_module.dart';

import '../../../feature/feature_toggle.dart';
import '../module_names.dart';

/// Feature Toggle Module for managing feature flags and feature availability
class FeatureToggleModule implements FeatureModule {
  @override
  String get name => EvoAppModuleNames.featureToggle;

  @override
  List<Type> get dependencies => [
    FeatureToggle,
  ];

  @override
  Future<void> register(GetIt getIt) async {
    // Register FeatureToggle singleton
    getIt.registerLazySingleton<FeatureToggle>(() => FeatureToggle());
  }
}
