import 'package:flutter_common_package/base/module/feature_module.dart';
import 'package:get_it/get_it.dart';

import '../module_names.dart';

/// UI Utility Module that provides UI-related utility dependencies.
///
/// This module handles UI utilities that don't fit into theme or components,
/// such as navigation observers, UI helpers, and other UI-related utilities.
class UiUtilityModule implements FeatureModule {
  @override
  String get name => EvoAppModuleNames.uiUtility;

  @override
  List<Type> get dependencies => [
    // Add UI utility dependencies here when needed
  ];

  @override
  Future<void> register(GetIt getIt) async {
    // Register UI utility dependencies here when needed
    // This module is currently a placeholder for future UI utilities
  }
}
