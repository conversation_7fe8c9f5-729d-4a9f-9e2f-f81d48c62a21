import 'package:flutter_common_package/base/module/feature_module.dart';
import 'package:flutter_common_package/util/common_navigator.dart';
import 'package:get_it/get_it.dart';

import '../module_names.dart';
import '../../../util/navigator/evo_router_navigator.dart';
import '../../../feature/logging/evo_navigator_observer.dart';

/// Navigation module that provides routing and navigation dependencies.
///
/// This module handles app routing, navigation observers, and navigation utilities.
/// It integrates with the logging module for navigation tracking.
class EvoNavigationModule implements FeatureModule {
  @override
  String get name => EvoAppModuleNames.navigation;

  @override
  List<Type> get dependencies => [
    CommonNavigator,
    EvoNavigatorObserver,
  ];

  @override
  Future<void> register(GetIt getIt) async {
    // Register common navigator
    if (!getIt.isRegistered<CommonNavigator>()) {
      getIt.registerLazySingleton<CommonNavigator>(() => EvoRouterNavigator());
    }

    // Register navigator observer
    if (!getIt.isRegistered<EvoNavigatorObserver>()) {
      getIt.registerLazySingleton<EvoNavigatorObserver>(() => EvoNavigatorObserver());
    }
  }
}
