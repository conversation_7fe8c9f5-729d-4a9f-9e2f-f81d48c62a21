import 'package:get_it/get_it.dart';
import 'package:flutter_common_package/base/module/feature_module.dart';

import '../module_names.dart';
import '../../../feature/splash_screen/utils/secure_detection_utils/secure_detection.dart';
import '../../../feature/splash_screen/utils/secure_detection_utils/secure_detection_impl.dart';
import '../../../feature/splash_screen/utils/exit_app_feature/exit_app_feature.dart';
import '../../../feature/splash_screen/utils/exit_app_feature/exit_app_feature_impl.dart';

/// Security module that provides security detection and app exit functionality.
///
/// This module handles detecting security issues like jailbreak/root detection
/// and provides functionality for safely exiting the app when needed.
class SecurityModule implements FeatureModule {
  @override
  String get name => EvoAppModuleNames.security;

  @override
  List<Type> get dependencies => [
    SecureDetection,
    ExitAppFeature,
  ];

  @override
  Future<void> register(GetIt getIt) async {
    // Register secure detection implementation
    if (!getIt.isRegistered<SecureDetection>()) {
      getIt.registerLazySingleton<SecureDetection>(() => SecureDetectionImpl());
    }

    // Register exit app feature
    if (!getIt.isRegistered<ExitAppFeature>()) {
      getIt.registerLazySingleton<ExitAppFeature>(() => ExitAppFeatureImpl());
    }
  }
}
