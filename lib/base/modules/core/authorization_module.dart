import 'package:flutter_common_package/base/module/feature_module.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get_it/get_it.dart';

import '../module_names.dart';
import '../../../data/repository/authentication_repo.dart';
import '../../../data/repository/authentication_repo_impl.dart';
import '../../../data/repository/user_repo.dart';
import '../../../data/repository/user_repo_impl.dart';
import '../../../feature/authorization_session_expired/authorization_session_expired.dart';
import '../../../feature/authorization_session_expired/authorization_session_expired_popup.dart';
import '../../../feature/authorization_session_expired/force_logout_popup.dart';
import '../../../util/secure_storage_helper/secure_storage_helper.dart';
import '../../../util/secure_storage_helper/secure_storage_helper_impl.dart';
import '../../../util/token_utils/jwt_helper.dart';
import '../../../util/token_utils/mock_jwt_helper.dart';
import '../../../prepare_for_app_initiation.dart';

const String nonAuthenticationHttpClientInstance = 'NonAuthenticationHttpClient';

/// Authorization module that provides authentication and session management dependencies.
///
/// This module handles user authentication, token management, JWT operations,
/// and session state management including expiration handling.
class AuthorizationModule implements FeatureModule {
  @override
  String get name => EvoAppModuleNames.authorization;

  @override
  List<Type> get dependencies => [
    // Authentication dependencies
    AuthenticationRepo,
    UserRepo,
    JwtHelper,
    EvoLocalStorageHelper,

    // Session management dependencies
    AuthorizationSessionExpiredHandler,
    AuthorizationSessionExpiredPopup,
    ForceLogoutPopup,
  ];

  @override
  Future<void> register(GetIt getIt) async {
    // Register JWT helper
    if (!getIt.isRegistered<JwtHelper>()) {
      getIt.registerLazySingleton<JwtHelper>(() => MockJwtHelper());
    }

    // Register secure storage helper
    if (!getIt.isRegistered<EvoLocalStorageHelper>()) {
      getIt.registerLazySingleton<EvoLocalStorageHelper>(
          () => EvoSecureStorageHelperImpl(secureStorage: getIt.get<FlutterSecureStorage>()));
    }

    // Register authentication repository
    if (!getIt.isRegistered<AuthenticationRepo>()) {
      getIt.registerLazySingleton<AuthenticationRepo>(
        () => AuthenticationRepoImpl(
          evoHttpClient: getIt.get<CommonHttpClient>(),
          nonAuthenticationEvoHttpClient:
              getIt.get<CommonHttpClient>(instanceName: nonAuthenticationHttpClientInstance),
          evoLocalStorageHelper: getIt.get<EvoLocalStorageHelper>(),
          appState: getIt.get<AppState>(),
        ),
      );
    }

    // Register user repository
    if (!getIt.isRegistered<UserRepo>()) {
      getIt.registerLazySingleton<UserRepo>(() => UserRepoImpl(
            getIt.get<CommonHttpClient>(),
            getIt.get<EvoLocalStorageHelper>(),
            getIt.get<AppState>(),
          ));
    }

    // Register authorization session handlers
    if (!getIt.isRegistered<AuthorizationSessionExpiredHandler>()) {
      getIt.registerLazySingleton<AuthorizationSessionExpiredHandler>(
          () => AuthorizationSessionExpiredHandlerImpl());
    }

    // Register popups for session expiration
    if (!getIt.isRegistered<AuthorizationSessionExpiredPopup>()) {
      getIt.registerLazySingleton<AuthorizationSessionExpiredPopup>(
          () => AuthorizationSessionExpiredPopup());
    }

    if (!getIt.isRegistered<ForceLogoutPopup>()) {
      getIt.registerLazySingleton<ForceLogoutPopup>(
          () => ForceLogoutPopup());
    }
  }
}
