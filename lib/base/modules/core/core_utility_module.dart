import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/module/feature_module.dart';
import 'package:get_it/get_it.dart';

import '../../../prepare_for_app_initiation.dart';
import '../../../util/functions.dart';
import '../module_names.dart';
import 'evo_util_function_module.dart';

/// Core Utility Module that provides essential application utilities and state management
class CoreUtilityModule implements FeatureModule {
  @override
  String get name => EvoAppModuleNames.coreUtility;

  @override
  List<Type> get dependencies => [
    AppState,
    EvoUtilFunction,
    EvoFlutterWrapper,
  ];

  @override
  Future<void> register(GetIt getIt) async {
    // Register AppState as a singleton
    if (!getIt.isRegistered<AppState>()) {
      getIt.registerLazySingleton<AppState>(() => AppState());
    }

    // Register EvoUtilFunction as a singleton
    if (!getIt.isRegistered<EvoUtilFunction>()) {
      getIt.registerLazySingleton<EvoUtilFunction>(() => EvoUtilFunction());
    }

    // Register EvoFlutterWrapper as a singleton
    if (!getIt.isRegistered<EvoFlutterWrapper>()) {
      getIt.registerLazySingleton<EvoFlutterWrapper>(() => EvoFlutterWrapper());
    }
  }
}
